const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // File operations
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  
  // Platform info
  platform: process.platform,
  
  // Window controls
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),
  
  // Data export/import helpers
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),

  // Print functionality
  print: () => ipc<PERSON>enderer.invoke('print-page'),
  printContent: (htmlContent, options) => ipcRenderer.invoke('print-content', htmlContent, options),

  // Notification
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),

  // Database operations
  // المواد (النظام القديم)
  getMaterials: () => ipcRenderer.invoke('db-get-materials'),
  addMaterial: (material) => ipcRenderer.invoke('db-add-material', material),

  // فئات المواد التفصيلية
  getMaterialCategories: () => ipcRenderer.invoke('db-get-material-categories'),
  addMaterialCategory: (category) => ipcRenderer.invoke('db-add-material-category', category),

  // المواد التفصيلية
  getDetailedMaterials: () => ipcRenderer.invoke('db-get-detailed-materials'),
  getDetailedMaterialsByCategory: (categoryId) => ipcRenderer.invoke('db-get-detailed-materials-by-category', categoryId),
  addDetailedMaterial: (material) => ipcRenderer.invoke('db-add-detailed-material', material),
  updateDetailedMaterial: (id, updates) => ipcRenderer.invoke('db-update-detailed-material', id, updates),
  deleteDetailedMaterial: (id) => ipcRenderer.invoke('db-delete-detailed-material', id),
  searchDetailedMaterials: (query) => ipcRenderer.invoke('db-search-detailed-materials', query),
  getDetailedMaterial: (id) => ipcRenderer.invoke('db-get-detailed-material', id),
  updateMaterialStock: (materialId, quantityChange) => ipcRenderer.invoke('db-update-material-stock', materialId, quantityChange),

  // مواد المشاريع
  getProjectMaterials: (projectId) => ipcRenderer.invoke('db-get-project-materials', projectId),
  addProjectMaterial: (projectMaterial) => ipcRenderer.invoke('db-add-project-material', projectMaterial),
  updateProjectMaterial: (id, updates) => ipcRenderer.invoke('db-update-project-material', id, updates),
  deleteProjectMaterial: (id) => ipcRenderer.invoke('db-delete-project-material', id),

  // العمال
  getWorkers: () => ipcRenderer.invoke('db-get-workers'),
  addWorker: (worker) => ipcRenderer.invoke('db-add-worker', worker),

  // المصانع
  getFactories: () => ipcRenderer.invoke('db-get-factories'),
  addFactory: (factory) => ipcRenderer.invoke('db-add-factory', factory),

  // المصممين
  getDesigners: () => ipcRenderer.invoke('db-get-designers'),
  addDesigner: (designer) => ipcRenderer.invoke('db-add-designer', designer),

  // المشاريع
  getProjects: () => ipcRenderer.invoke('db-get-projects'),
  addProject: (project) => ipcRenderer.invoke('db-add-project', project),
  updateProject: (id, updates) => ipcRenderer.invoke('db-update-project', id, updates),

  // العملاء
  getCustomers: () => ipcRenderer.invoke('db-get-customers'),
  addCustomer: (customer) => ipcRenderer.invoke('db-add-customer', customer),

  // الموظفين
  getEmployees: () => ipcRenderer.invoke('db-get-employees'),
  addEmployee: (employee) => ipcRenderer.invoke('db-add-employee', employee),

  // المعاملات المالية
  getCashTransactions: () => ipcRenderer.invoke('db-get-cash-transactions'),
  addCashTransaction: (transaction) => ipcRenderer.invoke('db-add-cash-transaction', transaction),
  getCashSummary: () => ipcRenderer.invoke('db-get-cash-summary'),

  // فئات المواد التفصيلية
  getMaterialCategories: () => ipcRenderer.invoke('db-get-material-categories'),
  addMaterialCategory: (category) => ipcRenderer.invoke('db-add-material-category', category),

  // المواد التفصيلية
  getDetailedMaterials: () => ipcRenderer.invoke('db-get-detailed-materials'),
  getDetailedMaterialsByCategory: (categoryId) => ipcRenderer.invoke('db-get-detailed-materials-by-category', categoryId),
  addDetailedMaterial: (material) => ipcRenderer.invoke('db-add-detailed-material', material)
});

// Add some basic security
window.addEventListener('DOMContentLoaded', () => {
  // Disable context menu in production
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
  }
  
  // Disable drag and drop
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });
  
  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });
});
