import { app, BrowserWindow, Menu, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// استيراد قاعدة البيانات
import { createRequire } from 'module';
import fs from 'fs';
const require = createRequire(import.meta.url);
const { initializeDatabase, runQuery, getQuery, allQuery, closeDatabase } = require('./database-electron.cjs');

// تحديد ما إذا كان في وضع التطوير أم الإنتاج
const distPath = path.join(__dirname, '../dist/index.html');
const isDev = !fs.existsSync(distPath);

console.log('مسار ملف البناء:', distPath);
console.log('هل الملف موجود؟', fs.existsSync(distPath));
console.log('وضع التشغيل:', isDev ? 'تطوير' : 'إنتاج');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, 'icon.png'),
    titleBarStyle: 'default',
    show: false,
    autoHideMenuBar: false
  });

  // Load the app
  let startUrl;
  if (isDev) {
    startUrl = 'http://localhost:5173';
    console.log('تشغيل في وضع التطوير - محاولة الاتصال بالخادم المحلي');
  } else {
    // استخدام مسار مطلق لتجنب مشاكل التشفير
    const indexPath = path.resolve(__dirname, '../dist/index.html');
    // تحويل المسار إلى URL صحيح باستخدام pathToFileURL
    startUrl = require('url').pathToFileURL(indexPath).href;
    console.log('تشغيل في وضع الإنتاج - تحميل الملفات المحلية');
    console.log('مسار الملف:', startUrl);
  }

  mainWindow.loadURL(startUrl);

  // معالج الأخطاء في تحميل الصفحة
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('فشل في تحميل الصفحة:', errorDescription);
    console.error('الرابط:', validatedURL);
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus(); // التأكد من أن النافذة في المقدمة

    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }

    console.log('تم فتح نافذة التطبيق بنجاح');
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Create application menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول التطبيق',
              message: 'نظام إدارة مصنع الأثاث',
              detail: 'نظام متكامل لإدارة جميع عمليات مصنع الأثاث\nالإصدار: 1.0.0',
              buttons: ['موافق']
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  try {
    await initializeDatabase();
    console.log('تم تهيئة قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error);
  }
  createWindow();
});

app.on('window-all-closed', () => {
  closeDatabase();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for communication with renderer process
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  return result;
});

ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });
  return result;
});

// معالج الطباعة
ipcMain.handle('print-page', async () => {
  try {
    await mainWindow.webContents.print({
      silent: false,
      printBackground: true,
      color: true,
      margins: {
        marginType: 'printableArea'
      },
      landscape: false,
      scaleFactor: 100
    });
    return true;
  } catch (error) {
    console.error('خطأ في الطباعة:', error);
    return false;
  }
});

// معالج طباعة محتوى مخصص
ipcMain.handle('print-content', async (event, htmlContent, options = {}) => {
  try {
    // إنشاء نافذة مخفية للطباعة
    const printWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // تحميل المحتوى
    await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

    // انتظار تحميل المحتوى
    await new Promise(resolve => {
      printWindow.webContents.once('did-finish-load', resolve);
    });

    // طباعة المحتوى
    await printWindow.webContents.print({
      silent: false,
      printBackground: true,
      color: true,
      margins: {
        marginType: 'printableArea'
      },
      landscape: options.landscape || false,
      scaleFactor: options.scaleFactor || 100
    });

    // إغلاق النافذة
    printWindow.close();
    return true;
  } catch (error) {
    console.error('خطأ في طباعة المحتوى:', error);
    return false;
  }
});

// معالجات قاعدة البيانات
// المواد
ipcMain.handle('db-get-materials', async () => {
  try {
    return await allQuery('SELECT * FROM materials ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المواد:', error);
    return [];
  }
});

ipcMain.handle('db-add-material', async (event, material) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO materials (id, name, description, pricePerSqm, category) VALUES (?, ?, ?, ?, ?)',
      [id, material.name, material.description, material.pricePerSqm, material.category]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المادة:', error);
    return null;
  }
});

// فئات المواد التفصيلية
ipcMain.handle('db-get-material-categories', async () => {
  try {
    return await allQuery('SELECT * FROM material_categories ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب فئات المواد:', error);
    return [];
  }
});

ipcMain.handle('db-add-material-category', async (event, category) => {
  try {
    const id = category.id || Date.now().toString();
    await runQuery(
      'INSERT OR IGNORE INTO material_categories (id, name, description, color) VALUES (?, ?, ?, ?)',
      [id, category.name, category.description, category.color]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة فئة المواد:', error);
    return null;
  }
});

// المواد التفصيلية
ipcMain.handle('db-get-detailed-materials', async () => {
  try {
    const materials = await allQuery(`
      SELECT dm.*, mc.name as categoryName, mc.description as categoryDescription, mc.color as categoryColor
      FROM detailed_materials dm
      LEFT JOIN material_categories mc ON dm.categoryId = mc.id
      WHERE dm.isActive = 1
      ORDER BY dm.code, dm.name
    `);

    return materials.map(material => ({
      ...material,
      category: {
        id: material.categoryId,
        name: material.categoryName,
        description: material.categoryDescription,
        color: material.categoryColor
      }
    }));
  } catch (error) {
    console.error('خطأ في جلب المواد التفصيلية:', error);
    return [];
  }
});

ipcMain.handle('db-get-detailed-materials-by-category', async (event, categoryId) => {
  try {
    const materials = await allQuery(`
      SELECT dm.*, mc.name as categoryName, mc.description as categoryDescription, mc.color as categoryColor
      FROM detailed_materials dm
      LEFT JOIN material_categories mc ON dm.categoryId = mc.id
      WHERE dm.categoryId = ? AND dm.isActive = 1
      ORDER BY dm.code, dm.name
    `, [categoryId]);

    return materials.map(material => ({
      ...material,
      category: {
        id: material.categoryId,
        name: material.categoryName,
        description: material.categoryDescription,
        color: material.categoryColor
      }
    }));
  } catch (error) {
    console.error('خطأ في جلب المواد حسب الفئة:', error);
    return [];
  }
});

ipcMain.handle('db-add-detailed-material', async (event, material) => {
  try {
    const id = Date.now().toString();
    await runQuery(`
      INSERT INTO detailed_materials
      (id, code, name, description, categoryId, unit, purchasePrice, salePrice,
       availableQuantity, minQuantity, supplier, notes, isActive)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, material.code, material.name, material.description, material.category.id,
      material.unit, material.purchasePrice, material.salePrice,
      material.availableQuantity, material.minQuantity, material.supplier,
      material.notes, material.isActive ? 1 : 0
    ]);
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المادة التفصيلية:', error);
    return null;
  }
});

ipcMain.handle('db-update-detailed-material', async (event, id, updates) => {
  try {
    const fields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
      if (key === 'category' && updates[key]) {
        fields.push('categoryId = ?');
        values.push(updates[key].id);
      } else if (key !== 'id' && key !== 'createdAt' && key !== 'category') {
        fields.push(`${key} = ?`);
        values.push(updates[key]);
      }
    });

    if (fields.length > 0) {
      fields.push('updatedAt = CURRENT_TIMESTAMP');
      values.push(id);

      await runQuery(
        `UPDATE detailed_materials SET ${fields.join(', ')} WHERE id = ?`,
        values
      );
    }

    return true;
  } catch (error) {
    console.error('خطأ في تحديث المادة التفصيلية:', error);
    return false;
  }
});

ipcMain.handle('db-delete-detailed-material', async (event, id) => {
  try {
    await runQuery('UPDATE detailed_materials SET isActive = 0 WHERE id = ?', [id]);
    return true;
  } catch (error) {
    console.error('خطأ في حذف المادة التفصيلية:', error);
    return false;
  }
});

ipcMain.handle('db-search-detailed-materials', async (event, query) => {
  try {
    const materials = await allQuery(`
      SELECT dm.*, mc.name as categoryName, mc.description as categoryDescription, mc.color as categoryColor
      FROM detailed_materials dm
      LEFT JOIN material_categories mc ON dm.categoryId = mc.id
      WHERE dm.isActive = 1 AND (
        dm.name LIKE ? OR
        dm.code LIKE ? OR
        dm.description LIKE ?
      )
      ORDER BY dm.code, dm.name
    `, [`%${query}%`, `%${query}%`, `%${query}%`]);

    return materials.map(material => ({
      ...material,
      category: {
        id: material.categoryId,
        name: material.categoryName,
        description: material.categoryDescription,
        color: material.categoryColor
      }
    }));
  } catch (error) {
    console.error('خطأ في البحث عن المواد:', error);
    return [];
  }
});

ipcMain.handle('db-get-detailed-material', async (event, id) => {
  try {
    const materials = await allQuery(`
      SELECT dm.*, mc.name as categoryName, mc.description as categoryDescription, mc.color as categoryColor
      FROM detailed_materials dm
      LEFT JOIN material_categories mc ON dm.categoryId = mc.id
      WHERE dm.id = ?
    `, [id]);

    if (materials.length > 0) {
      const material = materials[0];
      return {
        ...material,
        category: {
          id: material.categoryId,
          name: material.categoryName,
          description: material.categoryDescription,
          color: material.categoryColor
        }
      };
    }
    return null;
  } catch (error) {
    console.error('خطأ في جلب المادة:', error);
    return null;
  }
});

ipcMain.handle('db-update-material-stock', async (event, materialId, quantityChange) => {
  try {
    await runQuery(
      'UPDATE detailed_materials SET availableQuantity = availableQuantity + ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [quantityChange, materialId]
    );
    return true;
  } catch (error) {
    console.error('خطأ في تحديث المخزن:', error);
    return false;
  }
});

// مواد المشاريع
ipcMain.handle('db-get-project-materials', async (event, projectId) => {
  try {
    const materials = await allQuery(`
      SELECT pm.*, dm.code, dm.name, dm.unit, dm.purchasePrice, dm.salePrice,
             mc.name as categoryName, mc.color as categoryColor
      FROM project_materials pm
      LEFT JOIN detailed_materials dm ON pm.materialId = dm.id
      LEFT JOIN material_categories mc ON dm.categoryId = mc.id
      WHERE pm.projectId = ?
      ORDER BY dm.code
    `, [projectId]);

    return materials.map(material => ({
      ...material,
      material: {
        id: material.materialId,
        code: material.code,
        name: material.name,
        unit: material.unit,
        purchasePrice: material.purchasePrice,
        salePrice: material.salePrice,
        category: {
          name: material.categoryName,
          color: material.categoryColor
        }
      }
    }));
  } catch (error) {
    console.error('خطأ في جلب مواد المشروع:', error);
    return [];
  }
});

ipcMain.handle('db-add-project-material', async (event, projectMaterial) => {
  try {
    const id = Date.now().toString();
    await runQuery(`
      INSERT INTO project_materials
      (id, projectId, materialId, requiredQuantity, usedQuantity,
       totalPurchaseCost, totalSaleCost, profit, source, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, projectMaterial.projectId, projectMaterial.materialId,
      projectMaterial.requiredQuantity, projectMaterial.usedQuantity || 0,
      projectMaterial.totalPurchaseCost, projectMaterial.totalSaleCost,
      projectMaterial.profit, projectMaterial.source, projectMaterial.notes
    ]);
    return id;
  } catch (error) {
    console.error('خطأ في إضافة مادة المشروع:', error);
    return null;
  }
});

ipcMain.handle('db-update-project-material', async (event, id, updates) => {
  try {
    const fields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
      if (key !== 'id' && key !== 'createdAt') {
        fields.push(`${key} = ?`);
        values.push(updates[key]);
      }
    });

    if (fields.length > 0) {
      fields.push('updatedAt = CURRENT_TIMESTAMP');
      values.push(id);

      await runQuery(
        `UPDATE project_materials SET ${fields.join(', ')} WHERE id = ?`,
        values
      );
    }

    return true;
  } catch (error) {
    console.error('خطأ في تحديث مادة المشروع:', error);
    return false;
  }
});

ipcMain.handle('db-delete-project-material', async (event, id) => {
  try {
    await runQuery('DELETE FROM project_materials WHERE id = ?', [id]);
    return true;
  } catch (error) {
    console.error('خطأ في حذف مادة المشروع:', error);
    return false;
  }
});

// العمال
ipcMain.handle('db-get-workers', async () => {
  try {
    return await allQuery('SELECT * FROM workers ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب العمال:', error);
    return [];
  }
});

ipcMain.handle('db-add-worker', async (event, worker) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO workers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, worker.name, worker.specialty, worker.pricePerSqm, worker.phone]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة العامل:', error);
    return null;
  }
});

// المصانع
ipcMain.handle('db-get-factories', async () => {
  try {
    return await allQuery('SELECT * FROM factories ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المصانع:', error);
    return [];
  }
});

ipcMain.handle('db-add-factory', async (event, factory) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO factories (id, name, specialty, pricePerSqm, location) VALUES (?, ?, ?, ?, ?)',
      [id, factory.name, factory.specialty, factory.pricePerSqm, factory.location]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المصنع:', error);
    return null;
  }
});

// المصممين
ipcMain.handle('db-get-designers', async () => {
  try {
    return await allQuery('SELECT * FROM designers ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب المصممين:', error);
    return [];
  }
});

ipcMain.handle('db-add-designer', async (event, designer) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO designers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, designer.name, designer.specialty, designer.pricePerSqm, designer.phone]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المصمم:', error);
    return null;
  }
});



// المشاريع
ipcMain.handle('db-get-projects', async () => {
  try {
    const projects = await allQuery(`
      SELECT p.*,
             m.name as materialName, m.pricePerSqm as materialPrice,
             w.name as workerName, w.pricePerSqm as workerPrice,
             f.name as factoryName, f.pricePerSqm as factoryPrice,
             d.name as designerName, d.pricePerSqm as designerPrice
      FROM projects p
      LEFT JOIN materials m ON p.materialId = m.id
      LEFT JOIN workers w ON p.workerId = w.id
      LEFT JOIN factories f ON p.factoryId = f.id
      LEFT JOIN designers d ON p.designerId = d.id
      ORDER BY p.createdAt DESC
    `);

    return projects.map(project => ({
      ...project,
      selectedMaterial: project.materialId ? {
        id: project.materialId,
        name: project.materialName,
        pricePerSqm: project.materialPrice
      } : null,
      selectedWorker: {
        id: project.workerId,
        name: project.workerName,
        pricePerSqm: project.workerPrice
      },
      selectedFactory: {
        id: project.factoryId,
        name: project.factoryName,
        pricePerSqm: project.factoryPrice
      },
      selectedDesigner: {
        id: project.designerId,
        name: project.designerName,
        pricePerSqm: project.designerPrice
      },
      breakdown: {
        materialCost: project.materialCost,
        workerCost: project.workerCost,
        factoryCost: project.factoryCost,
        designerCost: project.designerCost
      }
    }));
  } catch (error) {
    console.error('خطأ في جلب المشاريع:', error);
    return [];
  }
});

ipcMain.handle('db-add-project', async (event, project) => {
  try {
    const id = Date.now().toString();
    await runQuery(`
      INSERT INTO projects (
        id, customerName, customerPhone, area, furnitureType,
        materialId, workerId, factoryId, designerId,
        totalCost, materialCost, workerCost, factoryCost, designerCost,
        paidAmount, remainingAmount, status, invoiceStatus, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, project.customerName, project.customerPhone, project.area, project.furnitureType,
      project.selectedMaterial?.id || null, project.selectedWorker.id, project.selectedFactory.id, project.selectedDesigner.id,
      project.totalCost, project.breakdown.materialCost, project.breakdown.workerCost,
      project.breakdown.factoryCost, project.breakdown.designerCost,
      project.paidAmount, project.remainingAmount, project.status, project.invoiceStatus, project.notes
    ]);

    // تحديث إحصائيات العميل
    await updateCustomerStats(project.customerName, project.customerPhone, project.totalCost);

    return id;
  } catch (error) {
    console.error('خطأ في إضافة المشروع:', error);
    return null;
  }
});

ipcMain.handle('db-update-project', async (event, id, updates) => {
  try {
    const fields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
      if (key === 'selectedMaterial' && updates[key]) {
        fields.push('materialId = ?');
        values.push(updates[key].id);
      } else if (key === 'selectedWorker' && updates[key]) {
        fields.push('workerId = ?');
        values.push(updates[key].id);
      } else if (key === 'selectedFactory' && updates[key]) {
        fields.push('factoryId = ?');
        values.push(updates[key].id);
      } else if (key === 'selectedDesigner' && updates[key]) {
        fields.push('designerId = ?');
        values.push(updates[key].id);
      } else if (key === 'breakdown' && updates[key]) {
        fields.push('materialCost = ?', 'workerCost = ?', 'factoryCost = ?', 'designerCost = ?');
        values.push(updates[key].materialCost, updates[key].workerCost, updates[key].factoryCost, updates[key].designerCost);
      } else if (key !== 'id' && key !== 'createdAt' && !key.startsWith('selected') && key !== 'breakdown') {
        fields.push(`${key} = ?`);
        values.push(updates[key]);
      }
    });

    if (fields.length > 0) {
      fields.push('updatedAt = CURRENT_TIMESTAMP');
      values.push(id);

      await runQuery(
        `UPDATE projects SET ${fields.join(', ')} WHERE id = ?`,
        values
      );
    }

    return true;
  } catch (error) {
    console.error('خطأ في تحديث المشروع:', error);
    return false;
  }
});

// العملاء
ipcMain.handle('db-get-customers', async () => {
  try {
    return await allQuery('SELECT * FROM customers ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    return [];
  }
});

ipcMain.handle('db-add-customer', async (event, customer) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO customers (id, name, phone, email, address, totalProjects, totalSpent) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [id, customer.name, customer.phone, customer.email, customer.address, customer.totalProjects || 0, customer.totalSpent || 0]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error);
    return null;
  }
});

// الموظفين
ipcMain.handle('db-get-employees', async () => {
  try {
    return await allQuery('SELECT * FROM employees ORDER BY name');
  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    return [];
  }
});

ipcMain.handle('db-add-employee', async (event, employee) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO employees (id, name, position, baseSalary, bonuses, deductions, totalSalary, phone, hireDate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [id, employee.name, employee.position, employee.baseSalary, employee.bonuses || 0, employee.deductions || 0, employee.totalSalary, employee.phone, employee.hireDate]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    return null;
  }
});

// المعاملات المالية
ipcMain.handle('db-get-cash-transactions', async () => {
  try {
    return await allQuery('SELECT * FROM cash_transactions ORDER BY date DESC, createdAt DESC');
  } catch (error) {
    console.error('خطأ في جلب المعاملات المالية:', error);
    return [];
  }
});

ipcMain.handle('db-add-cash-transaction', async (event, transaction) => {
  try {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO cash_transactions (id, type, category, amount, description, projectId, employeeId, date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [id, transaction.type, transaction.category, transaction.amount, transaction.description, transaction.projectId, transaction.employeeId, transaction.date]
    );
    return id;
  } catch (error) {
    console.error('خطأ في إضافة المعاملة المالية:', error);
    return null;
  }
});

ipcMain.handle('db-get-cash-summary', async () => {
  try {
    const incomeResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "دخل"');
    const expenseResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "مصروف"');

    const totalIncome = incomeResult?.total || 0;
    const totalExpenses = expenseResult?.total || 0;
    const currentBalance = totalIncome - totalExpenses;

    // إحصائيات شهرية
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
    const monthlyIncomeResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "دخل" AND date LIKE ?', [`${currentMonth}%`]);
    const monthlyExpenseResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "مصروف" AND date LIKE ?', [`${currentMonth}%`]);

    const monthlyIncome = monthlyIncomeResult?.total || 0;
    const monthlyExpenses = monthlyExpenseResult?.total || 0;

    // إحصائيات تفصيلية
    const projectPaymentsResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "دخل" AND category = "دفعة مشروع"');
    const salaryPaymentsResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "مصروف" AND category = "راتب"');
    const generalExpensesResult = await getQuery('SELECT SUM(amount) as total FROM cash_transactions WHERE type = "مصروف" AND category != "راتب"');

    return {
      totalIncome,
      totalExpenses,
      currentBalance,
      monthlyIncome,
      monthlyExpenses,
      projectPayments: projectPaymentsResult?.total || 0,
      salaryPayments: salaryPaymentsResult?.total || 0,
      generalExpenses: generalExpensesResult?.total || 0
    };
  } catch (error) {
    console.error('خطأ في جلب ملخص الخزينة:', error);
    return {
      totalIncome: 0,
      totalExpenses: 0,
      currentBalance: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      projectPayments: 0,
      salaryPayments: 0,
      generalExpenses: 0
    };
  }
});

// وظائف مساعدة
const updateCustomerStats = async (name, phone, projectCost) => {
  try {
    const existing = await getQuery('SELECT * FROM customers WHERE name = ?', [name]);

    if (existing) {
      await runQuery(
        'UPDATE customers SET totalProjects = totalProjects + 1, totalSpent = totalSpent + ?, phone = COALESCE(?, phone), updatedAt = CURRENT_TIMESTAMP WHERE name = ?',
        [projectCost || 0, phone, name]
      );
    } else {
      const id = Date.now().toString();
      await runQuery(
        'INSERT INTO customers (id, name, phone, totalProjects, totalSpent) VALUES (?, ?, ?, ?, ?)',
        [id, name, phone, 1, projectCost || 0]
      );
    }
  } catch (error) {
    console.error('خطأ في تحديث إحصائيات العميل:', error);
  }
};
